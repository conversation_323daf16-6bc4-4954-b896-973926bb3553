// Define a platform channel
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

const platform = MethodChannel('smsAutoForwarderAppChannel');

// Start the background service from Flutter
Future<void> startBackgroundService() async {
  try {
    await platform.invokeMethod('registerSmsBackgroundReceiver');
  } catch (e) {
    // This is not critical since the receiver is already registered in AndroidManifest.xml
  }
}

Future<void> sendAuthStateToAndroid(String uid) async {
  // Get the current user's ID token for authentication
  String? authToken;
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      authToken = await user.getIdToken();
    }
  } catch (e) {
    print('Error getting auth token: $e');
  }

  await platform.invokeMethod('sendAuthStateToAndroid', {
    'uid': uid,
    'authToken': authToken,
  });
}

Future<void> stopBackgroundService() async {
  await platform.invokeMethod('stopBackgroundService');
}
